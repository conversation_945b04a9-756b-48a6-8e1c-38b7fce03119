import { format } from "date-fns"
import { ar } from "date-fns/locale"
import type { User } from "@/stores/app-store"

// Export formats
export type ExportFormat = "csv" | "excel" | "pdf"

// Filter types for export
export interface ExportFilters {
  year?: string
  college?: string
  gender?: string
  status?: string
  dateRange?: {
    from: string
    to: string
  }
}

// Import result type
export interface ImportResult {
  success: boolean
  data?: User[]
  errors?: string[]
  warnings?: string[]
  totalRows?: number
  validRows?: number
}

/**
 * Export users data to CSV format
 */
export function exportToCSV(users: User[], filename?: string): void {
  const headers = [
    "الاسم",
    "الهاتف", 
    "النوع",
    "السنة",
    "الكلية",
    "القسم",
    "تاريخ الميلاد",
    "العنوان",
    "الفيسبوك",
    "تاريخ أول حضور",
    "تاريخ الإنشاء"
  ]

  const csvContent = [
    headers.join(","),
    ...users.map(user => [
      `"${user.name}"`,
      `"${user.phone}"`,
      user.gender === "male" ? "ذكر" : "أنثى",
      user.year.toString(),
      `"${user.college}"`,
      `"${user.department}"`,
      user.birthdate,
      `"${user.address}"`,
      `"${user.facebook_url || ""}"`,
      user.first_attendance_date,
      format(new Date(user.created_at), "yyyy-MM-dd")
    ].join(","))
  ].join("\n")

  // Add BOM for proper Arabic text encoding
  const BOM = "\uFEFF"
  const blob = new Blob([BOM + csvContent], {
    type: "text/csv;charset=utf-8;"
  })

  const link = document.createElement("a")
  link.href = URL.createObjectURL(blob)
  link.download = filename || `users_${format(new Date(), "yyyy-MM-dd")}.csv`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

/**
 * Export users data to Excel format (using CSV with Excel-compatible formatting)
 */
export function exportToExcel(users: User[], filename?: string): void {
  // For now, we'll use CSV format that Excel can open properly
  // In a real implementation, you'd use a library like xlsx
  const headers = [
    "الاسم",
    "الهاتف",
    "النوع", 
    "السنة",
    "الكلية",
    "القسم",
    "تاريخ الميلاد",
    "العنوان",
    "الفيسبوك",
    "تاريخ أول حضور",
    "تاريخ الإنشاء"
  ]

  const csvContent = [
    headers.join("\t"), // Use tabs for Excel compatibility
    ...users.map(user => [
      user.name,
      user.phone,
      user.gender === "male" ? "ذكر" : "أنثى",
      user.year.toString(),
      user.college,
      user.department,
      user.birthdate,
      user.address,
      user.facebook_url || "",
      user.first_attendance_date,
      format(new Date(user.created_at), "yyyy-MM-dd")
    ].join("\t"))
  ].join("\n")

  const BOM = "\uFEFF"
  const blob = new Blob([BOM + csvContent], {
    type: "application/vnd.ms-excel;charset=utf-8;"
  })

  const link = document.createElement("a")
  link.href = URL.createObjectURL(blob)
  link.download = filename || `users_${format(new Date(), "yyyy-MM-dd")}.xls`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

/**
 * Export users data to PDF format
 */
export function exportToPDF(users: User[], filename?: string): void {
  // Create a simple HTML table for PDF generation
  const htmlContent = `
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
      <meta charset="UTF-8">
      <title>قائمة المستخدمين</title>
      <style>
        body { 
          font-family: Arial, sans-serif; 
          direction: rtl; 
          text-align: right;
          margin: 20px;
        }
        table { 
          width: 100%; 
          border-collapse: collapse; 
          margin-top: 20px;
        }
        th, td { 
          border: 1px solid #ddd; 
          padding: 8px; 
          text-align: right;
        }
        th { 
          background-color: #f2f2f2; 
          font-weight: bold;
        }
        .header {
          text-align: center;
          margin-bottom: 20px;
        }
        .stats {
          margin-bottom: 20px;
          padding: 10px;
          background-color: #f9f9f9;
          border-radius: 5px;
        }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>قائمة المستخدمين</h1>
        <p>تاريخ التصدير: ${format(new Date(), "dd MMMM yyyy", { locale: ar })}</p>
      </div>
      
      <div class="stats">
        <p><strong>إجمالي المستخدمين:</strong> ${users.length}</p>
        <p><strong>الذكور:</strong> ${users.filter(u => u.gender === "male").length}</p>
        <p><strong>الإناث:</strong> ${users.filter(u => u.gender === "female").length}</p>
      </div>

      <table>
        <thead>
          <tr>
            <th>الاسم</th>
            <th>الهاتف</th>
            <th>النوع</th>
            <th>السنة</th>
            <th>الكلية</th>
            <th>القسم</th>
            <th>تاريخ الميلاد</th>
            <th>العنوان</th>
          </tr>
        </thead>
        <tbody>
          ${users.map(user => `
            <tr>
              <td>${user.name}</td>
              <td>${user.phone}</td>
              <td>${user.gender === "male" ? "ذكر" : "أنثى"}</td>
              <td>السنة ${user.year}</td>
              <td>${user.college}</td>
              <td>${user.department}</td>
              <td>${format(new Date(user.birthdate), "dd/MM/yyyy")}</td>
              <td>${user.address}</td>
            </tr>
          `).join("")}
        </tbody>
      </table>
    </body>
    </html>
  `

  // Create a new window and print
  const printWindow = window.open("", "_blank")
  if (printWindow) {
    printWindow.document.write(htmlContent)
    printWindow.document.close()
    printWindow.focus()
    
    // Wait for content to load then print
    setTimeout(() => {
      printWindow.print()
      printWindow.close()
    }, 1000)
  }
}

/**
 * Filter users based on export filters
 */
export function filterUsersForExport(users: User[], filters?: ExportFilters): User[] {
  if (!filters) return users

  let filtered = users

  // Year filter
  if (filters.year && filters.year !== "all") {
    filtered = filtered.filter(user => user.year.toString() === filters.year)
  }

  // College filter
  if (filters.college && filters.college !== "all") {
    filtered = filtered.filter(user => 
      user.college.toLowerCase().includes(filters.college!.toLowerCase())
    )
  }

  // Gender filter
  if (filters.gender && filters.gender !== "all") {
    filtered = filtered.filter(user => user.gender === filters.gender)
  }

  // Status filter (placeholder for future implementation)
  if (filters.status && filters.status !== "all") {
    // For now, all users are considered active
    // This can be extended when user status is implemented
  }

  // Date range filter (based on creation date)
  if (filters.dateRange?.from) {
    filtered = filtered.filter(user => 
      new Date(user.created_at) >= new Date(filters.dateRange!.from)
    )
  }

  if (filters.dateRange?.to) {
    filtered = filtered.filter(user => 
      new Date(user.created_at) <= new Date(filters.dateRange!.to)
    )
  }

  return filtered
}

/**
 * Main export function that handles all formats
 */
export function exportUsers(
  users: User[], 
  format: ExportFormat, 
  filters?: ExportFilters,
  filename?: string
): void {
  const filteredUsers = filterUsersForExport(users, filters)
  
  if (filteredUsers.length === 0) {
    alert("لا توجد بيانات للتصدير بناءً على الفلاتر المحددة")
    return
  }

  switch (format) {
    case "csv":
      exportToCSV(filteredUsers, filename)
      break
    case "excel":
      exportToExcel(filteredUsers, filename)
      break
    case "pdf":
      exportToPDF(filteredUsers, filename)
      break
    default:
      throw new Error(`Unsupported export format: ${format}`)
  }
}

/**
 * Parse CSV content and validate user data
 */
export function parseCSVImport(csvContent: string): ImportResult {
  try {
    const lines = csvContent.split("\n").filter(line => line.trim())
    
    if (lines.length < 2) {
      return {
        success: false,
        errors: ["الملف فارغ أو لا يحتوي على بيانات صالحة"]
      }
    }

    const headers = lines[0].split(",").map(h => h.trim().replace(/"/g, ""))
    const dataLines = lines.slice(1)
    
    const users: User[] = []
    const errors: string[] = []
    const warnings: string[] = []

    dataLines.forEach((line, index) => {
      try {
        const values = line.split(",").map(v => v.trim().replace(/"/g, ""))
        
        if (values.length < 8) {
          errors.push(`الصف ${index + 2}: بيانات ناقصة`)
          return
        }

        const user: User = {
          id: `imported_${Date.now()}_${index}`,
          name: values[0] || "",
          phone: values[1] || "",
          gender: values[2] === "أنثى" ? "female" : "male",
          year: parseInt(values[3]) as 1 | 2 | 3 | 4,
          college: values[4] || "",
          department: values[5] || "",
          birthdate: values[6] || "",
          address: values[7] || "",
          facebook_url: values[8] || "",
          first_attendance_date: values[9] || format(new Date(), "yyyy-MM-dd"),
          qr_code: `QR_${Date.now()}_${index}`,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }

        // Validation
        if (!user.name) {
          errors.push(`الصف ${index + 2}: الاسم مطلوب`)
          return
        }

        if (!user.phone || user.phone.length < 11) {
          errors.push(`الصف ${index + 2}: رقم الهاتف غير صحيح`)
          return
        }

        if (!user.year || user.year < 1 || user.year > 4) {
          errors.push(`الصف ${index + 2}: السنة الدراسية غير صحيحة`)
          return
        }

        users.push(user)
      } catch (error) {
        errors.push(`الصف ${index + 2}: خطأ في تحليل البيانات`)
      }
    })

    return {
      success: errors.length === 0,
      data: users,
      errors: errors.length > 0 ? errors : undefined,
      warnings: warnings.length > 0 ? warnings : undefined,
      totalRows: dataLines.length,
      validRows: users.length
    }
  } catch (error) {
    return {
      success: false,
      errors: ["خطأ في قراءة الملف. تأكد من أن الملف بصيغة CSV صحيحة"]
    }
  }
}
