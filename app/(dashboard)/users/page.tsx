"use client";

import { useState, useEffect, useMemo, useCallback } from "react";
import { useRouter } from "next/navigation";
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { Separator } from "@/components/ui/separator";
import {
  Search,
  Filter,
  Plus,
  MoreHorizontal,
  Edit,
  Trash2,
  QrCode,
  Calendar,
  GraduationCap,
  ArrowUpDown,
  ChevronLeft,
  ChevronRight,
  Download,
  Upload,
  Users,
  CheckCircle,
  X,
  Phone,
  MapPin,
  Facebook,
  Eye,
  UserCheck,
  Settings,
  FileText,
  BarChart3,
  UserPlus,
  RefreshCw,
  SlidersHorizontal,
  Grid3X3,
  List,
  ChevronDown,
} from "lucide-react";
import { useAppStore, type User as AppUser } from "@/stores/app-store";
import { useRTL } from "@/contexts/rtl-context";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { ar } from "date-fns/locale";
import { toast } from "sonner";
import { UserEditModal } from "@/components/users/user-edit-modal";

// Enhanced filter types
interface UserFilters {
  search: string;
  year: string;
  college: string;
  gender: string;
  status: string;
  dateRange: {
    from: string;
    to: string;
  };
}

// View modes
type ViewMode = "table" | "grid";

export default function UsersPage() {
  const router = useRouter();
  const {
    users,
    deleteUser,
    updateUser,
    setSelectedUser,
    exportUsers,
    bulkMarkAttendance,
    loading,
    error,
  } = useAppStore();
  const { isRTL, direction } = useRTL();

  // State management
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = useState({});
  const [globalFilter, setGlobalFilter] = useState("");

  // Modal states
  const [showQRModal, setShowQRModal] = useState<AppUser | null>(null);
  const [showUserDetails, setShowUserDetails] = useState<AppUser | null>(null);
  const [showEditModal, setShowEditModal] = useState<AppUser | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState<AppUser | null>(
    null
  );

  // UI states
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [viewMode, setViewMode] = useState<ViewMode>("table");
  const [showFilters, setShowFilters] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Enhanced filters
  const [filters, setFilters] = useState<UserFilters>({
    search: "",
    year: "all",
    college: "all",
    gender: "all",
    status: "all",
    dateRange: {
      from: "",
      to: "",
    },
  });

  // Debounced search
  useEffect(() => {
    const timer = setTimeout(() => {
      setGlobalFilter(filters.search);
    }, 300);
    return () => clearTimeout(timer);
  }, [filters.search]);

  // Filter users based on advanced filters
  const filteredUsers = useMemo(() => {
    let filtered = users;

    // Year filter
    if (filters.year !== "all") {
      filtered = filtered.filter(
        (user) => user.year.toString() === filters.year
      );
    }

    // College filter
    if (filters.college !== "all") {
      filtered = filtered.filter((user) =>
        user.college.toLowerCase().includes(filters.college.toLowerCase())
      );
    }

    // Gender filter
    if (filters.gender !== "all") {
      filtered = filtered.filter((user) => user.gender === filters.gender);
    }

    // Status filter (can be extended for active/inactive users)
    if (filters.status !== "all") {
      // For now, all users are considered active
      // This can be extended when user status is implemented
    }

    return filtered;
  }, [users, filters]);

  // Statistics
  const stats = useMemo(() => {
    const total = filteredUsers.length;
    const byYear = {
      1: filteredUsers.filter((u) => u.year === 1).length,
      2: filteredUsers.filter((u) => u.year === 2).length,
      3: filteredUsers.filter((u) => u.year === 3).length,
      4: filteredUsers.filter((u) => u.year === 4).length,
    };
    const byGender = {
      male: filteredUsers.filter((u) => u.gender === "male").length,
      female: filteredUsers.filter((u) => u.gender === "female").length,
    };

    return { total, byYear, byGender };
  }, [filteredUsers]);

  // Table columns definition
  const columns: ColumnDef<AppUser>[] = [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={table.getIsAllPageRowsSelected()}
          onCheckedChange={(value) => {
            table.toggleAllPageRowsSelected(!!value);
            if (value) {
              setSelectedUsers(
                table.getRowModel().rows.map((row) => row.original.id)
              );
            } else {
              setSelectedUsers([]);
            }
          }}
          aria-label="تحديد الكل"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={selectedUsers.includes(row.original.id)}
          onCheckedChange={(value) => {
            if (value) {
              setSelectedUsers([...selectedUsers, row.original.id]);
            } else {
              setSelectedUsers(
                selectedUsers.filter((id) => id !== row.original.id)
              );
            }
          }}
          aria-label="تحديد الصف"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "name",
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-auto p-0 font-medium"
        >
          الاسم
          <ArrowUpDown className="mr-2 h-4 w-4" />
        </Button>
      ),
      cell: ({ row }) => (
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
            <span className="text-white text-sm font-bold">
              {row.original.name.charAt(0)}
            </span>
          </div>
          <div>
            <div className="font-medium">{row.getValue("name")}</div>
            <div className="text-sm text-gray-500 flex items-center gap-2">
              <Phone className="h-3 w-3" />
              {row.original.phone}
            </div>
          </div>
        </div>
      ),
    },
    {
      accessorKey: "year",
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-auto p-0 font-medium"
        >
          السنة
          <ArrowUpDown className="mr-2 h-4 w-4" />
        </Button>
      ),
      cell: ({ row }) => (
        <Badge variant="outline" className="font-medium">
          السنة {row.getValue("year")}
        </Badge>
      ),
    },
    {
      accessorKey: "college",
      header: "الكلية",
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <GraduationCap className="h-4 w-4 text-gray-500" />
          <span className="max-w-[200px] truncate">
            {row.getValue("college")}
          </span>
        </div>
      ),
    },
    {
      accessorKey: "gender",
      header: "النوع",
      cell: ({ row }) => (
        <Badge
          variant={row.getValue("gender") === "male" ? "default" : "secondary"}
        >
          {row.getValue("gender") === "male" ? "ذكر" : "أنثى"}
        </Badge>
      ),
    },
    {
      accessorKey: "birthdate",
      header: "تاريخ الميلاد",
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <Calendar className="h-4 w-4 text-gray-500" />
          <span>
            {format(new Date(row.getValue("birthdate")), "dd/MM/yyyy")}
          </span>
        </div>
      ),
    },
    {
      id: "actions",
      enableHiding: false,
      cell: ({ row }) => {
        const user = row.original;

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>الإجراءات</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => setShowUserDetails(user)}>
                <Eye className="mr-2 h-4 w-4" />
                عرض التفاصيل
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setShowQRModal(user)}>
                <QrCode className="mr-2 h-4 w-4" />
                عرض QR Code
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setShowEditModal(user)}>
                <Edit className="mr-2 h-4 w-4" />
                تعديل
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => {
                  bulkMarkAttendance([user.id], true, "<EMAIL>");
                  toast.success(`تم تسجيل حضور ${user.name}`);
                }}
              >
                <UserCheck className="mr-2 h-4 w-4" />
                تسجيل حضور
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() => setShowDeleteConfirm(user)}
                className="text-red-600"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                حذف
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  // Table setup
  const table = useReactTable({
    data: filteredUsers,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    onGlobalFilterChange: setGlobalFilter,
    globalFilterFn: "includesString",
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
      globalFilter,
    },
  });

  return (
    <div className="p-6 space-y-6 page-transition font-cairo" dir={direction}>
      {/* Enhanced Header */}
      <div className="animate-fade-in">
        <div
          className={cn(
            "flex items-center justify-between",
            isRTL ? "flex-row-reverse" : "flex-row"
          )}
        >
          <div>
            <div
              className={cn(
                "flex items-center gap-3 mb-2",
                isRTL ? "flex-row-reverse" : "flex-row"
              )}
            >
              <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg">
                <Users className="h-6 w-6 text-white" />
              </div>
              <h1 className="text-4xl font-bold text-heading gradient-text">
                إدارة المستخدمين
              </h1>
              <Badge variant="outline" className="text-lg px-3 py-1">
                {stats.total} مستخدم
              </Badge>
            </div>
            <p
              className={cn(
                "text-gray-600 text-lg text-body",
                isRTL ? "text-right" : "text-left"
              )}
            >
              نظام إدارة شامل لجميع المستخدمين مع إمكانيات البحث والتصفية
              المتقدمة
            </p>
          </div>

          <div className="flex items-center gap-2">
            {/* View Mode Toggle */}
            <div className="flex items-center border rounded-lg p-1">
              <Button
                variant={viewMode === "table" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewMode("table")}
                className="h-8 w-8 p-0"
              >
                <List className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === "grid" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewMode("grid")}
                className="h-8 w-8 p-0"
              >
                <Grid3X3 className="h-4 w-4" />
              </Button>
            </div>

            {/* Refresh Button */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                setIsRefreshing(true);
                setTimeout(() => setIsRefreshing(false), 1000);
              }}
              disabled={isRefreshing}
            >
              <RefreshCw
                className={cn("h-4 w-4 mr-2", isRefreshing && "animate-spin")}
              />
              تحديث
            </Button>

            {/* Export Dropdown */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline">
                  <Download className="h-4 w-4 mr-2" />
                  تصدير
                  <ChevronDown className="h-4 w-4 ml-2" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem onClick={() => exportUsers("csv", filters)}>
                  <FileText className="h-4 w-4 mr-2" />
                  تصدير CSV
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => exportUsers("excel", filters)}>
                  <FileText className="h-4 w-4 mr-2" />
                  تصدير Excel
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => exportUsers("pdf", filters)}>
                  <FileText className="h-4 w-4 mr-2" />
                  تصدير PDF
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            {/* Import Button */}
            <Button variant="outline">
              <Upload className="h-4 w-4 mr-2" />
              استيراد
            </Button>

            {/* Add User Button */}
            <Button
              className="btn-gradient"
              onClick={() => router.push("/add-user")}
            >
              <UserPlus className="h-4 w-4 mr-2" />
              إضافة مستخدم
            </Button>
          </div>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 animate-fade-in">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">
                  إجمالي المستخدمين
                </p>
                <p className="text-2xl font-bold text-blue-600">
                  {stats.total}
                </p>
              </div>
              <Users className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">الذكور</p>
                <p className="text-2xl font-bold text-green-600">
                  {stats.byGender.male}
                </p>
              </div>
              <div className="h-8 w-8 bg-green-100 rounded-full flex items-center justify-center">
                <span className="text-green-600 font-bold">♂</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">الإناث</p>
                <p className="text-2xl font-bold text-pink-600">
                  {stats.byGender.female}
                </p>
              </div>
              <div className="h-8 w-8 bg-pink-100 rounded-full flex items-center justify-center">
                <span className="text-pink-600 font-bold">♀</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">
                  السنوات الدراسية
                </p>
                <div className="flex gap-1 mt-1">
                  {Object.entries(stats.byYear).map(([year, count]) => (
                    <Badge key={year} variant="outline" className="text-xs">
                      {year}: {count}
                    </Badge>
                  ))}
                </div>
              </div>
              <GraduationCap className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card className="animate-fade-in">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Search className="h-5 w-5" />
              البحث والتصفية
            </CardTitle>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowFilters(!showFilters)}
            >
              <SlidersHorizontal className="h-4 w-4 mr-2" />
              فلاتر متقدمة
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Main Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="البحث بالاسم، الهاتف، الكلية، أو القسم..."
              value={filters.search}
              onChange={(e) =>
                setFilters((prev) => ({ ...prev, search: e.target.value }))
              }
              className="pl-10 h-12 text-lg"
            />
          </div>

          {/* Advanced Filters */}
          {showFilters && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 p-4 bg-gray-50 rounded-lg">
              <div>
                <label className="text-sm font-medium text-gray-700 mb-2 block">
                  السنة الدراسية
                </label>
                <Select
                  value={filters.year}
                  onValueChange={(value) =>
                    setFilters((prev) => ({ ...prev, year: value }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="جميع السنوات" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">جميع السنوات</SelectItem>
                    <SelectItem value="1">السنة الأولى</SelectItem>
                    <SelectItem value="2">السنة الثانية</SelectItem>
                    <SelectItem value="3">السنة الثالثة</SelectItem>
                    <SelectItem value="4">السنة الرابعة</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-700 mb-2 block">
                  النوع
                </label>
                <Select
                  value={filters.gender}
                  onValueChange={(value) =>
                    setFilters((prev) => ({ ...prev, gender: value }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="جميع الأنواع" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">جميع الأنواع</SelectItem>
                    <SelectItem value="male">ذكر</SelectItem>
                    <SelectItem value="female">أنثى</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-700 mb-2 block">
                  الكلية
                </label>
                <Select
                  value={filters.college}
                  onValueChange={(value) =>
                    setFilters((prev) => ({ ...prev, college: value }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="جميع الكليات" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">جميع الكليات</SelectItem>
                    <SelectItem value="علوم">كلية العلوم</SelectItem>
                    <SelectItem value="تربية">كلية التربية</SelectItem>
                    <SelectItem value="هندسة">كلية الهندسة</SelectItem>
                    <SelectItem value="طب">كلية الطب</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-700 mb-2 block">
                  الحالة
                </label>
                <Select
                  value={filters.status}
                  onValueChange={(value) =>
                    setFilters((prev) => ({ ...prev, status: value }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="جميع الحالات" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">جميع الحالات</SelectItem>
                    <SelectItem value="active">نشط</SelectItem>
                    <SelectItem value="inactive">غير نشط</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Bulk Actions Bar */}
      {selectedUsers.length > 0 && (
        <Card className="animate-scale-in border-blue-200 bg-blue-50">
          <CardContent className="flex items-center justify-between p-4">
            <div className="flex items-center gap-3">
              <CheckCircle className="h-5 w-5 text-blue-600" />
              <span className="font-medium text-blue-800">
                تم تحديد {selectedUsers.length} مستخدم
              </span>
            </div>
            <div className="flex items-center gap-2">
              <Button
                size="sm"
                onClick={() => {
                  bulkMarkAttendance(selectedUsers, true, "<EMAIL>");
                  setSelectedUsers([]);
                  toast.success("تم تسجيل الحضور للمستخدمين المحددين");
                }}
                className="bg-green-600 hover:bg-green-700"
              >
                <UserCheck className="h-4 w-4 mr-2" />
                تسجيل حضور
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => {
                  bulkMarkAttendance(selectedUsers, false, "<EMAIL>");
                  setSelectedUsers([]);
                  toast.success("تم تسجيل الغياب للمستخدمين المحددين");
                }}
              >
                تسجيل غياب
              </Button>
              <Button
                size="sm"
                variant="destructive"
                onClick={() => {
                  if (
                    confirm(
                      `هل أنت متأكد من حذف ${selectedUsers.length} مستخدم؟`
                    )
                  ) {
                    selectedUsers.forEach((id) => deleteUser(id));
                    setSelectedUsers([]);
                    toast.success("تم حذف المستخدمين المحددين");
                  }
                }}
              >
                <Trash2 className="h-4 w-4 mr-2" />
                حذف
              </Button>
              <Button
                size="sm"
                variant="ghost"
                onClick={() => setSelectedUsers([])}
              >
                <X className="h-4 w-4 mr-2" />
                إلغاء التحديد
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Data Table */}
      <Card className="animate-fade-in">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <List className="h-5 w-5" />
              قائمة المستخدمين
            </CardTitle>
            <div className="flex items-center gap-2">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm">
                    <Settings className="h-4 w-4 mr-2" />
                    الأعمدة
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  {table
                    .getAllColumns()
                    .filter((column) => column.getCanHide())
                    .map((column) => {
                      return (
                        <DropdownMenuCheckboxItem
                          key={column.id}
                          className="capitalize"
                          checked={column.getIsVisible()}
                          onCheckedChange={(value) =>
                            column.toggleVisibility(!!value)
                          }
                        >
                          {column.id === "name" && "الاسم"}
                          {column.id === "year" && "السنة"}
                          {column.id === "college" && "الكلية"}
                          {column.id === "gender" && "النوع"}
                          {column.id === "birthdate" && "تاريخ الميلاد"}
                        </DropdownMenuCheckboxItem>
                      );
                    })}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                {table.getHeaderGroups().map((headerGroup) => (
                  <TableRow key={headerGroup.id}>
                    {headerGroup.headers.map((header) => {
                      return (
                        <TableHead key={header.id}>
                          {header.isPlaceholder
                            ? null
                            : flexRender(
                                header.column.columnDef.header,
                                header.getContext()
                              )}
                        </TableHead>
                      );
                    })}
                  </TableRow>
                ))}
              </TableHeader>
              <TableBody>
                {table.getRowModel().rows?.length ? (
                  table.getRowModel().rows.map((row) => (
                    <TableRow
                      key={row.id}
                      data-state={row.getIsSelected() && "selected"}
                      className="hover:bg-gray-50"
                    >
                      {row.getVisibleCells().map((cell) => (
                        <TableCell key={cell.id}>
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext()
                          )}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell
                      colSpan={columns.length}
                      className="h-24 text-center"
                    >
                      <div className="flex flex-col items-center gap-2">
                        <Users className="h-8 w-8 text-gray-400" />
                        <p className="text-gray-500">لا توجد نتائج</p>
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          <div className="flex items-center justify-between space-x-2 py-4">
            <div className="flex-1 text-sm text-muted-foreground">
              {table.getFilteredSelectedRowModel().rows.length} من{" "}
              {table.getFilteredRowModel().rows.length} صف محدد.
            </div>
            <div className="flex items-center space-x-2">
              <p className="text-sm font-medium">
                الصفحة {table.getState().pagination.pageIndex + 1} من{" "}
                {table.getPageCount()}
              </p>
              <Button
                variant="outline"
                size="sm"
                onClick={() => table.previousPage()}
                disabled={!table.getCanPreviousPage()}
              >
                <ChevronLeft className="h-4 w-4" />
                السابق
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => table.nextPage()}
                disabled={!table.getCanNextPage()}
              >
                التالي
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Modals */}
      <UserEditModal
        user={showEditModal}
        open={!!showEditModal}
        onOpenChange={(open) => {
          if (!open) setShowEditModal(null)
        }}
      />

      {/* QR Code Modal - TODO: Implement */}
      {showQRModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <Card className="w-full max-w-md animate-scale-in">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <QrCode className="h-5 w-5" />
                  QR Code - {showQRModal.name}
                </CardTitle>
                <Button variant="ghost" size="icon" onClick={() => setShowQRModal(null)}>
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </CardHeader>
            <CardContent className="text-center">
              <div className="w-48 h-48 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <QrCode className="h-16 w-16 text-gray-400" />
              </div>
              <p className="text-sm text-gray-600">QR Code: {showQRModal.qr_code}</p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* User Details Modal - TODO: Implement comprehensive view */}
      {showUserDetails && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <Card className="w-full max-w-2xl animate-scale-in max-h-[90vh] overflow-y-auto">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <Eye className="h-5 w-5" />
                  تفاصيل المستخدم - {showUserDetails.name}
                </CardTitle>
                <Button variant="ghost" size="icon" onClick={() => setShowUserDetails(null)}>
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-gray-500">الاسم الكامل</label>
                    <p className="text-lg font-medium">{showUserDetails.name}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">رقم الهاتف</label>
                    <p className="flex items-center gap-2">
                      <Phone className="h-4 w-4" />
                      {showUserDetails.phone}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">النوع</label>
                    <p>
                      <Badge variant={showUserDetails.gender === "male" ? "default" : "secondary"}>
                        {showUserDetails.gender === "male" ? "ذكر" : "أنثى"}
                      </Badge>
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">السنة الدراسية</label>
                    <p className="text-lg font-medium">السنة {showUserDetails.year}</p>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-gray-500">الكلية</label>
                    <p className="flex items-center gap-2">
                      <GraduationCap className="h-4 w-4" />
                      {showUserDetails.college}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">القسم</label>
                    <p>{showUserDetails.department}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">تاريخ الميلاد</label>
                    <p className="flex items-center gap-2">
                      <Calendar className="h-4 w-4" />
                      {format(new Date(showUserDetails.birthdate), "dd MMMM yyyy", { locale: ar })}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">أول حضور</label>
                    <p>{format(new Date(showUserDetails.first_attendance_date), "dd MMMM yyyy", { locale: ar })}</p>
                  </div>
                </div>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-500">العنوان</label>
                <p className="flex items-center gap-2">
                  <MapPin className="h-4 w-4" />
                  {showUserDetails.address}
                </p>
              </div>

              {showUserDetails.facebook_url && (
                <div>
                  <label className="text-sm font-medium text-gray-500">الفيسبوك</label>
                  <p className="flex items-center gap-2">
                    <Facebook className="h-4 w-4" />
                    <a
                      href={showUserDetails.facebook_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:underline"
                    >
                      {showUserDetails.facebook_url}
                    </a>
                  </p>
                </div>
              )}

              <div className="flex gap-2 pt-4 border-t">
                <Button onClick={() => setShowQRModal(showUserDetails)} variant="outline" className="flex-1">
                  <QrCode className="h-4 w-4 mr-2" />
                  عرض QR Code
                </Button>
                <Button onClick={() => {
                  setShowEditModal(showUserDetails)
                  setShowUserDetails(null)
                }} className="flex-1 btn-gradient">
                  <Edit className="h-4 w-4 mr-2" />
                  تعديل البيانات
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <Card className="w-full max-w-md animate-scale-in">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-red-600">
                <Trash2 className="h-5 w-5" />
                تأكيد الحذف
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 mb-4">
                هل أنت متأكد من حذف المستخدم <strong>{showDeleteConfirm.name}</strong>؟
                هذا الإجراء لا يمكن التراجع عنه.
              </p>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  onClick={() => setShowDeleteConfirm(null)}
                  className="flex-1"
                >
                  إلغاء
                </Button>
                <Button
                  variant="destructive"
                  onClick={() => {
                    deleteUser(showDeleteConfirm.id)
                    setShowDeleteConfirm(null)
                    toast.success("تم حذف المستخدم بنجاح")
                  }}
                  className="flex-1"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  حذف
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
